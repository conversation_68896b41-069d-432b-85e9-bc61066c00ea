<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页</title>
    <link rel="stylesheet" href="css/common.css">
    <style>
        /* 首页特有样式 */
        .hero {
            padding: 180px 0 120px;
            margin-top: 80px;
        }

        .hero h2 {
            font-size: 4.5rem;
            margin-bottom: 24px;
        }

        .hero p {
            font-size: 1.25rem;
            margin-bottom: 48px;
            max-width: 560px;
        }

        .hero-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            position: relative;
            z-index: 1;
        }

        .main-content {
            margin: 80px 0 0 0;
        }

        /* 首页特有样式 */
        /* 特点展示 */
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 32px;
            padding: 80px 0 60px;
            position: relative;
            z-index: 1;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            padding: 40px 32px;
            border-radius: 16px;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 119, 48, 0.1) 0%, rgba(120, 119, 198, 0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 119, 48, 0.3);
        }

        .feature-card:hover::before {
            opacity: 1;
        }

        .feature-icon {
            font-size: 3rem;
            background: linear-gradient(135deg, #ff7730 0%, #7877c6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 24px;
            position: relative;
            z-index: 1;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .feature-icon img {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            object-fit: cover;
            border: 2px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .feature-card:hover .feature-icon img {
            transform: scale(1.1);
            border-color: rgba(255, 119, 48, 0.5);
            box-shadow: 0 4px 16px rgba(255, 119, 48, 0.3);
        }

        .feature-card h3 {
            margin-bottom: 16px;
            font-size: 1.5rem;
            color: white;
            font-weight: 700;
            position: relative;
            z-index: 1;
            letter-spacing: -0.01em;
        }

        .feature-card p {
            color: rgba(255, 255, 255, 0.7);
            font-size: 1rem;
            line-height: 1.6;
            position: relative;
            z-index: 1;
            font-weight: 400;
        }

        /* 优势展示 */
        .advantages {
            background: transparent;
            padding: 120px 0;
            margin: 80px 0;
            position: relative;
        }

        .advantages::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 30% 20%, rgba(255, 119, 48, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%);
        }

        .advantages h2 {
            text-align: center;
            font-size: 3rem;
            margin-bottom: 80px;
            color: white;
            font-weight: 800;
            position: relative;
            z-index: 1;
            letter-spacing: -0.02em;
        }

        .advantages h2::after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #ff7730, #7877c6);
            border-radius: 2px;
        }

        .advantages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(360px, 1fr));
            gap: 32px;
            position: relative;
            z-index: 1;
        }

        .advantage-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            padding: 40px 32px;
            border-radius: 16px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .advantage-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, #ff7730, #7877c6);
        }

        .advantage-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 119, 48, 0.3);
            background: rgba(255, 255, 255, 0.08);
        }

        .advantage-icon {
            font-size: 2.5rem;
            background: linear-gradient(135deg, #ff7730 0%, #7877c6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
        }

        .advantage-card h3 {
            font-size: 1.25rem;
            color: white;
            margin-bottom: 16px;
            font-weight: 700;
            letter-spacing: -0.01em;
        }

        .advantage-card p {
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.6;
            font-size: 0.95rem;
            font-weight: 400;
        }

        /* 数据统计 */
        .stats {
            padding: 100px 0;
            text-align: center;
            background: rgba(15, 15, 15, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 24px;
            margin: 80px auto;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .stats::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 119, 48, 0.05) 0%, rgba(120, 119, 198, 0.05) 100%);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 40px;
            position: relative;
            z-index: 1;
        }

        .stat-item {
            padding: 32px 24px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 16px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-4px);
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(255, 119, 48, 0.3);
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.3);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 800;
            color: white;
            margin-bottom: 12px;
            background: linear-gradient(135deg, #ff7730 0%, #7877c6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1rem;
            font-weight: 500;
            letter-spacing: 0.5px;
        }

        /* 页脚样式 */
        .footer {
            background: rgba(10, 10, 10, 0.9);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: white;
            padding: 60px 0 30px;
            margin-top: 80px;
            position: relative;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 119, 48, 0.03) 0%, rgba(120, 119, 198, 0.03) 100%);
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            position: relative;
            z-index: 1;
        }

        .footer-section h4 {
            font-size: 1.125rem;
            margin-bottom: 20px;
            color: white;
            position: relative;
            padding-bottom: 12px;
            font-weight: 700;
            letter-spacing: -0.01em;
        }

        .footer-section h4::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 40px;
            height: 2px;
            background: linear-gradient(90deg, #ff7730, #7877c6);
            border-radius: 1px;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section ul li {
            margin-bottom: 12px;
        }

        .footer-section ul li a {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            display: inline-block;
            font-weight: 400;
        }

        .footer-section ul li a:hover {
            color: #ff7730;
            transform: translateX(4px);
        }

        .contact-info {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            color: rgba(255, 255, 255, 0.7);
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .contact-info:hover {
            color: white;
            transform: translateX(4px);
        }

        .contact-info i {
            margin-right: 12px;
            color: #ff7730;
            font-size: 1rem;
            width: 18px;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 30px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.5);
            position: relative;
            z-index: 1;
        }

        .footer-bottom p {
            margin-bottom: 8px;
            font-size: 0.8rem;
            line-height: 1.5;
            font-weight: 400;
        }

        .footer-bottom a {
            color: rgba(255, 255, 255, 0.7);
            transition: color 0.3s ease;
            text-decoration: none;
        }

        .footer-bottom a:hover {
            color: #ff7730;
        }

        /* 登录注册按钮样式 */
        .nav-auth {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .nav-auth a {
            padding: 10px 20px;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
            letter-spacing: 0.5px;
        }

        .nav-auth .login-btn {
            color: rgba(255, 255, 255, 0.8);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .nav-auth .login-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            transform: translateY(-1px);
        }

        .nav-auth .register-btn {
            background: linear-gradient(135deg, #7877c6 0%, #ff7730 100%);
            color: white;
            border: 1px solid rgba(120, 119, 198, 0.3);
        }

        .nav-auth .register-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 8px 25px rgba(120, 119, 198, 0.3);
        }

        /* 修改发布按钮样式 */
        .publish-btn {
            background: linear-gradient(135deg, #ff7730 0%, #ff4757 100%) !important;
            color: white !important;
            padding: 10px 20px;
            border-radius: 8px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            font-weight: 500;
            font-size: 14px;
            letter-spacing: 0.5px;
            border: 1px solid rgba(255, 119, 48, 0.3);
        }

        .publish-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 8px 25px rgba(255, 119, 48, 0.3);
        }

        .logout-btn {
            color: rgba(255, 255, 255, 0.8) !important;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            gap: 6px;
            font-weight: 500;
            font-size: 14px;
            letter-spacing: 0.5px;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white !important;
            transform: translateY(-1px);
        }

        .user-btns {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .user-btns a {
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 0 16px;
            }

            .hero {
                padding: 140px 0 100px;
            }

            .hero h2 {
                font-size: 3rem;
            }

            .hero p {
                font-size: 1.125rem;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
                gap: 16px;
            }

            .features {
                grid-template-columns: 1fr;
                gap: 24px;
                padding: 60px 0 40px;
            }

            .advantages {
                padding: 80px 0;
            }

            .advantages h2 {
                font-size: 2.5rem;
                margin-bottom: 60px;
            }

            .advantages-grid {
                grid-template-columns: 1fr;
                gap: 24px;
            }

            .stats {
                padding: 60px 0;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 24px;
            }

            .nav-links {
                gap: 24px;
            }

            .nav-auth {
                gap: 12px;
            }

            .nav-auth a,
            .user-btns a {
                padding: 8px 16px;
                font-size: 13px;
            }

            .footer {
                padding: 40px 0 20px;
            }

            .footer-content {
                gap: 32px;
            }
        }

        @media (max-width: 480px) {
            .hero h2 {
                font-size: 2.5rem;
            }

            .hero p {
                font-size: 1rem;
            }

            .advantages h2 {
                font-size: 2rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .nav-container {
                flex-direction: column;
                height: auto;
                gap: 16px;
                padding: 16px 0;
            }

            .nav-links {
                gap: 16px;
                flex-wrap: wrap;
                justify-content: center;
            }

            .nav-auth {
                gap: 8px;
            }

            .feature-card,
            .advantage-card {
                padding: 32px 24px;
            }
        }
    </style>
    <link rel="stylesheet" href="css/all.min.css">
</head>
<body>
    <div class="header" id="header">
        <div class="container nav-container">
            <div class="logo">
                <img src="img/logo.png" height="40" alt="Logo" />
            </div>
            <div class="nav-links">
                <a href="index.html" class="active">首页</a>
                <a href="products-app.html">游戏中心</a>
                <a href="articles.html">防沉迷公告</a>
                <a href="about.html">关于我们</a>
            </div>
            <div class="nav-auth" id="navAuth">
                <!-- 登录前显示 -->
                <div class="auth-btns" id="authBtns">
                    <a href="login.html" class="login-btn">
                        <i class="fas fa-user"></i>登录
                    </a>
                    <a href="register.html" class="register-btn">
                        <i class="fas fa-user-plus"></i>注册
                    </a>
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>发布
                    </a>
                </div>
                <!-- 登录后显示 -->
                <div class="user-btns" id="userBtns" style="display: none;">
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>发布
                    </a>
                    <a href="javascript:void(0)" class="logout-btn" onclick="handleLogout()">
                        <i class="fas fa-sign-out-alt"></i>退出
                    </a>
                </div>
            </div>
        </div>
    </div>
        
    <div class="hero">
        <div class="container">
            <h2>打造精品游戏，创造无限可能</h2>
            <p>专业的游戏开发团队，为您提供从创意到上线的一站式游戏开发服务</p>
            <div class="hero-buttons">
                <a href="about.html" class="hero-button primary-button">关于我们</a>
                <a href="products-app.html" class="hero-button secondary-button">查看游戏</a>
            </div>
        </div>
    </div>

    <div class="main-content">
        <div class="container">
            <div class="features" id="gamesShowcase">
                <!-- 游戏展示卡片将通过JavaScript动态生成 -->
            </div>
        </div>

    <div class="advantages">
        <div class="container">
            <h2>为什么选择我们的游戏开发服务？</h2>
            <div class="advantages-grid">
                <div class="advantage-card">
                    <div class="advantage-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <h3>专业游戏团队</h3>
                    <p>拥有多年游戏开发经验的专业团队，熟悉各种游戏类型和技术框架，确保项目高质量完成。</p>
                </div>
                <div class="advantage-card">
                    <div class="advantage-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h3>创意设计理念</h3>
                    <p>从游戏策划到美术设计，每个环节都注重创新与品质，打造独具特色的游戏体验。</p>
                </div>
                <div class="advantage-card">
                    <div class="advantage-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <h3>技术实力雄厚</h3>
                    <p>掌握Unity、Cocos2d等主流游戏引擎，支持2D/3D游戏开发，技术覆盖全平台。</p>
                </div>
                <div class="advantage-card">
                    <div class="advantage-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3>数据驱动运营</h3>
                    <p>提供专业的游戏数据分析和运营策略，帮助提升游戏留存率和收益表现。</p>
                </div>
                <div class="advantage-card">
                    <div class="advantage-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <h3>丰富发布经验</h3>
                    <p>熟悉各大应用商店和游戏平台的发布流程，帮助游戏快速上线并获得更好的推广效果。</p>
                </div>
                <div class="advantage-card">
                    <div class="advantage-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>防沉迷保障</h3>
                    <p>严格遵循国家相关法规，完善的防沉迷系统和实名认证机制，营造健康游戏环境。</p>
                </div>
            </div>
        </div>

        <!--
        <div class="stats">
            <div class="container">
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">200+</div>
                        <div class="stat-label">服务客户</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">500+</div>
                        <div class="stat-label">成功案例</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">50+</div>
                        <div class="stat-label">技术专家</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">98%</div>
                        <div class="stat-label">客户满意度</div>
                    </div>
                </div>
            </div>
        </div>
        -->
    </div>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>联系我们</h4>
                    <div class="contact-info">
                        <i class="fas fa-phone"></i>
                        <span>020-38383518</span>
                    </div>
                    <!-- <div class="contact-info">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </div> -->
                    <div class="contact-info">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>广州市天河区黄埔大道中660号之-2301、2302房</span>
                    </div>
                    <div class="contact-info">
                        <i class="fas fa-location"></i>
                        <span>广州立早网络科技有限公司</span>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>快速链接</h4>
                    <ul>
                        <li><a href="products-app.html">游戏中心</a></li>
                        <li><a href="articles.html">防沉迷公告</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>了解我们</h4>
                    <ul>
                        <li><a href="about.html">关于我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>抵制不良游戏，拒绝盗版游戏。注意自我保护，谨防受骗上当。适度游戏益脑，沉迷游戏伤身。合理安排时间，享受健康生活。</p>
                <p><a href="https://beian.miit.gov.cn" style="color:white">粤ICP备17009698号</a></p>
            </div>
        </div>
    </footer>

    <script src="js/login.js"></script>
    <script src="js/common.js"></script>
    <script>
        // 游戏数据（从products-app.html抽离）
        const gamesList = [
            {
                id: 'g1',
                name: '诸侯征战',
                image: 'img/app/1/icon.png',
                category: '战略',
                description: '一款复古纯粹的模拟经营三国手游。多人合作，疯狂国战，只有最好的策略才能赢得国战。'
            },
            {
                id: 'g2',
                name: '朕的江山2',
                image: 'img/app/2/icon.png',
                category: '战略',
                description: '《朕的江山2》是一款经典三国战役SLG手游。连城式大地图还原真实的三国战场。'
            },
            {
                id: 'g3',
                name: '不朽大陆',
                image: 'img/app/3/icon.png',
                category: '卡牌',
                description: '不朽大陆官方版是一款美漫式放置卡牌游戏，阻止外星人入侵，解放城市。'
            },
            {
                id: 'g4',
                name: '攻城掠地',
                image: 'img/app/4/icon.png',
                category: '策略',
                description: '《攻城掠地》是一款强调"国战"的战争策略游戏，实景还原三国战图。'
            },
            {
                id: 'g5',
                name: '割据天下',
                image: 'img/app/5/icon.png',
                category: '战略',
                description: '《割据天下》是一款文明题材策略战争手游。多元文化和不同种族的热血碰撞。'
            },
            {
                id: 'g6',
                name: '帝国霸权',
                image: 'img/app/6/icon.png',
                category: '战略',
                description: '帝国霸权是一款以中世纪时期罗马帝国为原型打造slg手游。'
            }
        ];

        // 动态生成游戏展示卡片
        function renderGamesShowcase() {
            const gamesContainer = document.getElementById('gamesShowcase');
            gamesContainer.innerHTML = '';

            gamesList.forEach(game => {
                const gameCard = document.createElement('div');
                gameCard.className = 'feature-card';
                gameCard.innerHTML = `
                    <div class="feature-icon">
                        <img src="${game.image}" alt="${game.name}">
                    </div>
                    <h3>${game.name}</h3>
                    <p>${game.description}</p>
                `;

                // 添加点击事件，跳转到游戏详情页
                gameCard.addEventListener('click', () => {
                    window.location.href = `product-detail.html?id=${game.id}`;
                });

                // 添加鼠标悬停效果
                gameCard.style.cursor = 'pointer';

                gamesContainer.appendChild(gameCard);
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            renderGamesShowcase();
        });
    </script>
</body>
</html>